// Tarayıcı önbelleği için Service Worker kaydı
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/service-worker.js').then(function(registration) {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);
        }, function(err) {
            console.log('ServiceWorker registration failed: ', err);
        });
    });
}

// Resimleri önbellek için önceden yükle
function preloadImages() {
    const images = ['images/bg.jpg', 'images/logo.jpg', 'images/1.png'];
    images.forEach(image => {
        const img = new Image();
        img.src = image;
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Resimleri önceden yükle
    preloadImages();

    // Anlık tarih bilgisini güncelle
    updateCurrentDate();

    // Rastgele istatistikler oluştur
    updateRandomStats();

    // Teşekkür mesajlarını yükle
    loadTestimonials();

    // Adım butonlarını ayarla
    setupStepButtons();

    // Cihaz seçimi
    setupDeviceSelection();

    // Proxy seçimi
    setupProxySelection();

    // Sayfa yükleme performansını ölç
    if (window.performance) {
        const perfData = window.performance.timing;
        const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
        console.log('Sayfa yükleme süresi: ' + pageLoadTime + 'ms');
    }
});

// Anlık tarih bilgisini güncelleme
function updateCurrentDate() {
    const currentDateElement = document.getElementById('current-date');
    const now = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    currentDateElement.textContent = now.toLocaleDateString('tr-TR', options);
}

// Günlük başarılı işlem sayısını güncelleme
function updateRandomStats() {
    const onlineCount = document.getElementById('online-count');
    const successCount = document.getElementById('success-count');

    // Online sayısını 5 saniyede bir +10/-10 şeklinde güncelle
    setInterval(() => {
        const randomChange = Math.floor(Math.random() * 21) - 10; // -10 ile +10 arasında
        let currentCount = parseInt(onlineCount.textContent);
        currentCount += randomChange;

        // Minimum 450, maksimum 550 kişi
        if (currentCount < 450) currentCount = 450;
        if (currentCount > 550) currentCount = 550;

        onlineCount.textContent = currentCount;
    }, 5000);

    // Saat 7'den başlayarak 24 saat boyunca 25'ten 744'e kadar artış
    function updateSuccessCount() {
        const now = new Date();
        const hour = now.getHours();
        const minute = now.getMinutes();

        // Saat 7'den itibaren geçen dakika sayısı
        let minutesSince7AM = 0;

        if (hour >= 7) {
            minutesSince7AM = (hour - 7) * 60 + minute;
        } else {
            minutesSince7AM = (hour + 17) * 60 + minute; // 24 - 7 = 17 (bir önceki günün 7'sinden itibaren)
        }

        // 24 saat = 1440 dakika
        // 25'ten 744'e = 719 artış
        // Her dakika için artış miktarı = 719 / 1440
        const increment = 719 / 1440;

        // Şu anki başarılı işlem sayısı
        const currentSuccessCount = Math.floor(25 + (minutesSince7AM * increment));

        // Maksimum 744
        const finalCount = Math.min(currentSuccessCount, 744);

        successCount.textContent = finalCount;
    }

    // Başlangıçta güncelle
    updateSuccessCount();

    // Her dakika güncelle
    setInterval(updateSuccessCount, 60000);
}

// Teşekkür mesajlarını yükleme
function loadTestimonials() {
    // Mesajları doğrudan tanımla
    const testimonials = [
        { name: "Ahmet", message: "Teşekkürler! 25000 Para hesabıma yüklendi!" },
        { name: "Mehmet", message: "Harika çalışıyor, arkadaşlarıma da önereceğim!" },
        { name: "Ayşe", message: "İnanamıyorum, gerçekten ücretsiz Para kazandım!" },
        { name: "Fatma", message: "Bu kadar kolay olacağını düşünmemiştim, teşekkürler!" },
        { name: "Ali", message: "Sonunda çalışan bir Para hilesi buldum!" },
        { name: "Zeynep", message: "SMS doğrulaması sonrası Paralar anında geldi!" },
        { name: "Mustafa", message: "Artık Critical Strike'ta rakiplerime fark atıyorum!" },
        { name: "Emine", message: "Bu hileyi keşfettiğim için çok mutluyum!" },
        { name: "Hüseyin", message: "Arkadaşlarım kıskançlıktan çatlıyor!" },
        { name: "Hatice", message: "Paralar sayesinde tüm karakterleri açtım!" },
        { name: "Osman", message: "Bu site sayesinde oyunda çok ilerledim!" },
        { name: "Gül", message: "Paralar gerçekten hesabıma yüklendi, teşekkürler!" },
        { name: "İbrahim", message: "Artık premium içeriklere erişebiliyorum!" },
        { name: "Sevgi", message: "Critical Strike'ta artık daha güçlüyüm!" },
        { name: "Kemal", message: "Arkadaşlarım nasıl bu kadar Para kazandığımı soruyor!" },
        { name: "Seda", message: "SMS doğrulaması çok kolaydı, Paralar anında geldi!" },
        { name: "Murat", message: "Bu hile sayesinde oyunda lider oldum!" },
        { name: "Deniz", message: "Gerçekten çalışıyor, herkese tavsiye ederim!" },
        { name: "Serkan", message: "Paralar sayesinde tüm karakterleri geliştirdim!" },
        { name: "Ece", message: "Bu kadar kolay olacağını düşünmemiştim, harika!" },
        { name: "Burak", message: "50000 Para kazandım, oyunda artık yenilmez oldum!" },
        { name: "Canan", message: "Arkadaşlarımın hepsine bu siteyi önerdim!" },
        { name: "Emre", message: "Critical Strike'ta artık en güçlü karakterlere sahibim!" },
        { name: "Gamze", message: "Paraları kullanarak tüm skin'leri aldım!" },
        { name: "Hakan", message: "Bu kadar hızlı olacağını tahmin etmemiştim!" },
        { name: "Jale", message: "Oyunda artık istediğim her şeyi alabiliyorum!" },
        { name: "Kadir", message: "Paraları kullanarak oyunda çok hızlı ilerledim!" },
        { name: "Leyla", message: "Critical Strike'ta artık en yüksek seviyedeyim!" },
        { name: "Metin", message: "Bu site sayesinde oyun deneyimim tamamen değişti!" },
        { name: "Nalan", message: "Paraları kullanarak tüm bölümleri geçtim!" },
        { name: "Orhan", message: "Arkadaşlarımın arasında en güçlü oyuncu benim!" }
    ];

    const testimonialsContainer = document.getElementById('testimonials');

    // Mesajları ekle
    testimonials.forEach(item => {
        const testimonialElement = document.createElement('div');
        testimonialElement.className = 'testimonial';
        testimonialElement.innerHTML = `<strong>${item.name}:</strong> ${item.message}`;
        testimonialsContainer.appendChild(testimonialElement);
    });

    // Mesajları tekrar ekleyerek sürekli akış sağla
    testimonials.forEach(item => {
        const testimonialElement = document.createElement('div');
        testimonialElement.className = 'testimonial';
        testimonialElement.innerHTML = `<strong>${item.name}:</strong> ${item.message}`;
        testimonialsContainer.appendChild(testimonialElement);
    });
}

// Adım butonlarını ayarlama
function setupStepButtons() {
    // Adım 1'den Adım 2'ye geçiş
    document.getElementById('step1-next').addEventListener('click', function() {
        const username = document.getElementById('username').value.trim();
        if (username === '') {
            alert('Lütfen kullanıcı adınızı girin!');
            return;
        }

        document.getElementById('step1').style.display = 'none';
        document.getElementById('step2').style.display = 'block';
    });

    // Adım 2'den Adım 3'e geçiş
    document.getElementById('step2-next').addEventListener('click', function() {
        const gemsInput = document.getElementById('gems-input');
        const gemsValue = parseInt(gemsInput.value);

        // Geçerli bir değer kontrolü
        if (isNaN(gemsValue) || gemsValue <= 0) {
            alert('Lütfen geçerli bir Para miktarı girin!');
            return;
        }

        document.getElementById('step2').style.display = 'none';
        document.getElementById('step3').style.display = 'block';
    });

    // Adım 3'ten Adım 4'e geçiş
    document.getElementById('step3-next').addEventListener('click', function() {
        document.getElementById('step3').style.display = 'none';
        document.getElementById('step4').style.display = 'block';

        // Generating işlemini başlat
        startGenerating();
    });
}

// Cihaz seçimi
function setupDeviceSelection() {
    const deviceButtons = document.querySelectorAll('.device-btn');

    deviceButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Aktif sınıfını kaldır
            deviceButtons.forEach(btn => btn.classList.remove('active'));

            // Tıklanan butona aktif sınıfını ekle
            this.classList.add('active');
        });
    });
}

// Para miktarı için herhangi bir ayarlama gerekmiyor

// Proxy seçimi
function setupProxySelection() {
    const proxyButtons = document.querySelectorAll('.proxy-btn');
    const nextButton = document.getElementById('step3-next');

    proxyButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Aktif sınıfını kaldır
            proxyButtons.forEach(btn => btn.classList.remove('active'));

            // Tıklanan butona aktif sınıfını ekle
            this.classList.add('active');

            // Devam butonunu aktifleştir
            nextButton.disabled = false;
        });
    });
}

// Generating işlemi
function startGenerating() {
    const consoleElement = document.getElementById('console');
    const username = document.getElementById('username').value;
    const gemsAmount = document.getElementById('gems-input').value;
    const device = document.querySelector('.device-btn.active').dataset.device;
    const proxy = document.querySelector('.proxy-btn.active').dataset.proxy;

    // Generating mesajları
    const messages = [
        `${username} isimli kullanıcı Critical Strike oyun datasında aranıyor...`,
        `${username} bulundu.`,
        `${gemsAmount} Para seçilen ${proxy} proxy üzerinden ${getDeviceName(device)} cihazındaki uygulamaya yükleniyor...`,
        `Bağlantı kuruluyor...`,
        `Sunucu yanıt veriyor...`,
        `Paralar hazırlanıyor...`,
        `Ücretsiz kullanıcı telefon SMS doğrulaması gerekiyor...`
    ];

    // Mesajları 10 saniyeye yay
    const totalDuration = 10000; // 10 saniye
    const messageInterval = totalDuration / messages.length;

    let index = 0;
    const interval = setInterval(() => {
        if (index < messages.length) {
            const messageElement = document.createElement('p');

            // Kullanıcı adı ve Para miktarını vurgula
            let messageText = messages[index];
            if (index === 0 || index === 1) {
                messageText = messageText.replace(username, `<span class="highlight-info">${username}</span>`);
            } else if (index === 2) {
                messageText = messageText.replace(username, `<span class="highlight-info">${username}</span>`);
                messageText = messageText.replace(gemsAmount, `<span class="highlight-info">${gemsAmount}</span>`);
                messageText = messageText.replace(getProxyName(proxy), `<span class="highlight-info">${getProxyName(proxy)}</span>`);
                messageText = messageText.replace(getDeviceName(device), `<span class="highlight-info">${getDeviceName(device)}</span>`);
            }

            messageElement.innerHTML = messageText;
            consoleElement.appendChild(messageElement);
            consoleElement.scrollTop = consoleElement.scrollHeight;
            index++;
        } else {
            clearInterval(interval);

            // Adım 5'e geç
            document.getElementById('step4').style.display = 'none';
            document.getElementById('step5').style.display = 'block';

            // Doğrulama bilgilerini ayarla
            setupVerification(username, gemsAmount, device, proxy);

            // Geri sayımı başlat
            startCountdown();
        }
    }, messageInterval);
}

// Doğrulama bilgilerini ayarlama
function setupVerification(username, gemsAmount, device, proxy) {
    document.getElementById('verify-username').textContent = username;
    document.getElementById('verify-gems').textContent = gemsAmount;
    document.getElementById('verify-device').textContent = getDeviceName(device);
    document.getElementById('verify-proxy').textContent = getProxyName(proxy);

    // CPA link butonunu ayarla (buraya CPA linkinizi ekleyin)
    document.getElementById('verify-button').href = "https://bigappboi.com/cl/i/lk41xd";
}

// Geri sayım
function startCountdown() {
    const countdownElement = document.getElementById('countdown-timer');
    let minutes = 10;
    let seconds = 0;

    const interval = setInterval(() => {
        if (seconds === 0) {
            if (minutes === 0) {
                clearInterval(interval);
                countdownElement.textContent = "Süre doldu!";
                return;
            }
            minutes--;
            seconds = 59;
        } else {
            seconds--;
        }

        // Zamanı formatlı göster
        const formattedMinutes = minutes.toString().padStart(2, '0');
        const formattedSeconds = seconds.toString().padStart(2, '0');
        countdownElement.textContent = `${formattedMinutes}:${formattedSeconds}`;
    }, 1000);
}

// Cihaz adını döndüren yardımcı fonksiyon
function getDeviceName(device) {
    switch(device) {
        case 'android': return 'Android';
        case 'ios': return 'iOS';
        case 'pc': return 'PC';
        case 'mac': return 'MAC';
        default: return device;
    }
}

// Proxy adını döndüren yardımcı fonksiyon
function getProxyName(proxy) {
    switch(proxy) {
        case 'avrupa': return 'Avrupa';
        case 'amerika': return 'Amerika';
        case 'afrika': return 'Afrika';
        case 'rusya': return 'Rusya';
        default: return proxy;
    }
}





